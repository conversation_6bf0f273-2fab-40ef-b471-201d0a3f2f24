{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "prebuild": "rm -rf .next", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "axios": "^1.11.0", "clsx": "^2.1.1", "lucide-react": "^0.541.0", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "typescript": "^5"}}