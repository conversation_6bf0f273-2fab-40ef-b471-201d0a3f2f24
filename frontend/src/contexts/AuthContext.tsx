'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { api } from '@/lib/api';
import { piUtils, PiAuthResult, PiUser } from '@/lib/pi';

interface User extends PiUser {
  id?: number;
  email?: string;
  isActive?: boolean;
  isVerified?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  signIn: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);

  const isAuthenticated = !!user;

  const refreshUser = React.useCallback(async () => {
    try {
      const response = await api.auth.me();
      if (response.data.user) {
        const userData = response.data.user;
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
      // If refresh fails, sign out
      setUser(null);
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
    }
  }, []);

  const checkAuthStatus = React.useCallback(async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      const savedUser = localStorage.getItem('user');

      if (token && savedUser) {
        setUser(JSON.parse(savedUser));
        // Optionally verify token with backend
        await refreshUser();
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      // Clear invalid auth data
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
    } finally {
      setLoading(false);
    }
  }, [refreshUser]);

  // Initialize Pi SDK on mount
  useEffect(() => {
    piUtils.init();
    checkAuthStatus();
  }, [checkAuthStatus]);



  const signIn = async () => {
    try {
      setLoading(true);

      if (!piUtils.isAvailable()) {
        throw new Error('Pi SDK is not available. Please open this app in Pi Browser.');
      }

      // Handle incomplete payments during authentication
      const handleIncompletePayment = async (payment: unknown) => {
        try {
          await api.payments.incomplete(payment);
        } catch (error) {
          console.error('Error handling incomplete payment:', error);
        }
      };

      // Authenticate with Pi Network
      const authResult: PiAuthResult = await piUtils.authenticate(handleIncompletePayment);

      // Send auth result to backend for verification
      const response = await api.auth.signin({ authResult });

      if (response.data.user) {
        const userData = response.data.user;
        setUser(userData);
        
        // Store auth data
        localStorage.setItem('authToken', authResult.accessToken);
        localStorage.setItem('user', JSON.stringify(userData));
      }
    } catch (error: unknown) {
      console.error('Sign in error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to sign in';
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      
      // Call backend signout endpoint
      await api.auth.signout();
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      // Clear local auth data regardless of backend response
      setUser(null);
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      setLoading(false);
    }
  };



  const value: AuthContextType = {
    user,
    loading,
    isAuthenticated,
    signIn,
    signOut,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
