'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import AuthProvider from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import {
  PlusIcon,
  FolderIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
} from '@heroicons/react/24/outline';

interface Project {
  id: number;
  name: string;
  description: string;
  owner_id: number;
  is_public: boolean;
  is_archived: boolean;
  created_at: string;
  updated_at: string;
  board_count?: number;
  task_count?: number;
}

const ProjectsPage: React.FC = () => {
  const { isAuthenticated, user: _user } = useAuth();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isAuthenticated) {
      fetchProjects();
    }
  }, [isAuthenticated]);

  const fetchProjects = async () => {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch('/api/v1/projects', {
      //   headers: {
      //     'Authorization': `Bearer ${token}`
      //   }
      // });
      // const data = await response.json();
      // setProjects(data);
      
      // Mock data for now
      setProjects([
        {
          id: 1,
          name: "Website Redesign",
          description: "Complete redesign of the company website with modern UI/UX",
          owner_id: 1,
          is_public: false,
          is_archived: false,
          created_at: "2024-01-15T10:00:00Z",
          updated_at: "2024-01-20T15:30:00Z",
          board_count: 3,
          task_count: 12
        },
        {
          id: 2,
          name: "Mobile App Development",
          description: "Native mobile app for iOS and Android platforms",
          owner_id: 1,
          is_public: true,
          is_archived: false,
          created_at: "2024-01-10T09:00:00Z",
          updated_at: "2024-01-22T11:45:00Z",
          board_count: 5,
          task_count: 28
        }
      ]);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-primary-500 mb-4">
            Please sign in to view your projects
          </h1>
          <Link href="/">
            <Button variant="primary">Go to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-primary-500">Projects</h1>
            <p className="text-gray-600 mt-2">
              Manage your projects and collaborate with your team
            </p>
          </div>
          <Link href="/projects/new">
            <Button variant="primary">
              <PlusIcon className="h-5 w-5 mr-2" />
              New Project
            </Button>
          </Link>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading projects...</p>
          </div>
        )}

        {/* Empty State */}
        {!loading && projects.length === 0 && (
          <div className="text-center py-12">
            <FolderIcon className="h-24 w-24 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-600 mb-2">
              No projects yet
            </h2>
            <p className="text-gray-500 mb-6">
              Create your first project to start organizing your work
            </p>
            <Link href="/projects/new">
              <Button variant="primary">
                <PlusIcon className="h-5 w-5 mr-2" />
                Create Project
              </Button>
            </Link>
          </div>
        )}

        {/* Projects Grid */}
        {!loading && projects.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {projects.map((project) => (
              <Link key={project.id} href={`/projects/${project.id}`}>
                <Card className="hover:border-primary-300 transition-colors cursor-pointer h-full">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-primary-500 mb-2">
                        {project.name}
                      </h3>
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {project.description}
                      </p>
                    </div>
                    {project.is_public && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full ml-2">
                        Public
                      </span>
                    )}
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <ClipboardDocumentListIcon className="h-4 w-4 mr-1" />
                        <span>{project.board_count || 0} boards</span>
                      </div>
                      <div className="flex items-center">
                        <UsersIcon className="h-4 w-4 mr-1" />
                        <span>{project.task_count || 0} tasks</span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <p className="text-xs text-gray-400">
                      Updated {new Date(project.updated_at).toLocaleDateString()}
                    </p>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        )}
      </main>
    </div>
  );
};

export default function Projects() {
  return (
    <AuthProvider>
      <ProjectsPage />
    </AuthProvider>
  );
}
