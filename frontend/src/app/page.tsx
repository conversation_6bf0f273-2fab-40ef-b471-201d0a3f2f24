'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import AuthProvider from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import {
  CheckCircleIcon,
  ClockIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';

const HomePage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();

  const features = [
    {
      icon: CheckCircleIcon,
      title: 'Task Management',
      description: 'Create, assign, and track tasks with deadlines and priorities.',
    },
    {
      icon: UserGroupIcon,
      title: 'Team Collaboration',
      description: 'Work together on boards with real-time updates and notifications.',
    },
    {
      icon: CurrencyDollarIcon,
      title: 'Pi Payments',
      description: 'Secure subscription payments using Pi cryptocurrency.',
    },
    {
      icon: ChartBarIcon,
      title: 'Progress Tracking',
      description: 'Monitor project progress with visual dashboards and reports.',
    },
    {
      icon: ClockIcon,
      title: 'Real-time Updates',
      description: 'Stay synchronized with instant updates across all devices.',
    },
    {
      icon: ShieldCheckIcon,
      title: 'Blockchain Security',
      description: 'Built on Pi Network for enhanced security and transparency.',
    },
  ];

  if (isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-primary-500 mb-2">
              Welcome back, {user?.username}!
            </h1>
            <p className="text-gray-600">
              Ready to manage your projects and collaborate with your team.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <Card>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Tasks</p>
                  <p className="text-2xl font-bold text-primary-500">12</p>
                </div>
                <CheckCircleIcon className="h-8 w-8 text-accent-500" />
              </div>
            </Card>

            <Card>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Team Members</p>
                  <p className="text-2xl font-bold text-primary-500">8</p>
                </div>
                <UserGroupIcon className="h-8 w-8 text-accent-500" />
              </div>
            </Card>

            <Card>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-primary-500">24</p>
                </div>
                <ChartBarIcon className="h-8 w-8 text-accent-500" />
              </div>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card>
              <h2 className="text-xl font-semibold text-primary-500 mb-4">Recent Tasks</h2>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">Update user interface</p>
                    <p className="text-sm text-gray-600">Due tomorrow</p>
                  </div>
                  <span className="px-2 py-1 text-xs font-medium bg-accent-100 text-accent-800 rounded-full">
                    In Progress
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">Fix authentication bug</p>
                    <p className="text-sm text-gray-600">Due in 3 days</p>
                  </div>
                  <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                    High Priority
                  </span>
                </div>
              </div>
              <div className="mt-4">
                <Link href="/tasks">
                  <Button variant="secondary" size="sm">
                    View All Tasks
                  </Button>
                </Link>
              </div>
            </Card>

            <Card>
              <h2 className="text-xl font-semibold text-primary-500 mb-4">Quick Actions</h2>
              <div className="space-y-3">
                <Link href="/tasks/new">
                  <Button variant="primary" className="w-full justify-start">
                    <CheckCircleIcon className="h-5 w-5 mr-2" />
                    Create New Task
                  </Button>
                </Link>
                <Link href="/boards/new">
                  <Button variant="secondary" className="w-full justify-start">
                    <UserGroupIcon className="h-5 w-5 mr-2" />
                    Create New Board
                  </Button>
                </Link>
                <Link href="/team">
                  <Button variant="ghost" className="w-full justify-start">
                    <UserGroupIcon className="h-5 w-5 mr-2" />
                    Invite Team Members
                  </Button>
                </Link>
              </div>
            </Card>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="relative bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <div className="mb-8">
              <Image
                src="/icon.svg"
                alt="Plover Icon"
                width={80}
                height={80}
                className="mx-auto mb-6"
              />
            </div>

            <h1 className="text-4xl md:text-6xl font-bold text-primary-500 mb-6">
              Project Management
              <span className="block text-accent-500">Powered by Pi</span>
            </h1>

            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Collaborate with your team, manage tasks, and track progress with blockchain-powered
              security and Pi Network integration.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="primary">
                Get Started with Pi
              </Button>
              <Button size="lg" variant="secondary">
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-primary-500 mb-4">
              Everything you need to manage projects
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Built for modern teams who value security, transparency, and seamless collaboration.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center">
                <feature.icon className="h-12 w-12 text-accent-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-primary-500 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to get started?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join thousands of teams already using Plover to manage their projects
            with Pi Network integration.
          </p>
          <Button size="lg" variant="accent">
            Sign in with Pi Network
          </Button>
        </div>
      </section>
    </div>
  );
};

export default function Home() {
  return (
    <AuthProvider>
      <HomePage />
    </AuthProvider>
  );
}
