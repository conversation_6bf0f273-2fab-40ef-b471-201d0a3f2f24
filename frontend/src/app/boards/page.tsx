'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import AuthProvider from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import {
  PlusIcon,
  ClipboardDocumentListIcon,
  UsersIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

interface Board {
  id: number;
  name: string;
  description: string;
  owner_id: number;
  project_id?: number;
  is_public: boolean;
  is_archived: boolean;
  created_at: string;
  updated_at: string;
  task_count?: number;
  member_count?: number;
  project?: {
    id: number;
    name: string;
  };
}

const BoardsPage: React.FC = () => {
  const { isAuthenticated, user: _user } = useAuth();
  const [boards, setBoards] = useState<Board[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isAuthenticated) {
      fetchBoards();
    }
  }, [isAuthenticated]);

  const fetchBoards = async () => {
    try {
      // TODO: Replace with actual API call
      // Mock data for now
      setBoards([
        {
          id: 1,
          name: "Design Sprint",
          description: "UI/UX design tasks for the new website",
          owner_id: 1,
          project_id: 1,
          is_public: false,
          is_archived: false,
          created_at: "2024-01-15T10:00:00Z",
          updated_at: "2024-01-20T15:30:00Z",
          task_count: 8,
          member_count: 3,
          project: { id: 1, name: "Website Redesign" }
        },
        {
          id: 2,
          name: "Development Tasks",
          description: "Frontend and backend development work",
          owner_id: 1,
          project_id: 1,
          is_public: true,
          is_archived: false,
          created_at: "2024-01-12T09:00:00Z",
          updated_at: "2024-01-22T11:45:00Z",
          task_count: 15,
          member_count: 5,
          project: { id: 1, name: "Website Redesign" }
        },
        {
          id: 3,
          name: "Personal Tasks",
          description: "My personal task management board",
          owner_id: 1,
          is_public: false,
          is_archived: false,
          created_at: "2024-01-10T08:00:00Z",
          updated_at: "2024-01-21T16:20:00Z",
          task_count: 4,
          member_count: 1
        }
      ]);
    } catch (error) {
      console.error('Failed to fetch boards:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-primary-500 mb-4">
            Please sign in to view your boards
          </h1>
          <Link href="/">
            <Button variant="primary">Go to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-primary-500">Boards</h1>
            <p className="text-gray-600 mt-2">
              Organize your tasks with Kanban-style boards
            </p>
          </div>
          <Link href="/boards/new">
            <Button variant="primary">
              <PlusIcon className="h-5 w-5 mr-2" />
              New Board
            </Button>
          </Link>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading boards...</p>
          </div>
        )}

        {/* Empty State */}
        {!loading && boards.length === 0 && (
          <div className="text-center py-12">
            <ClipboardDocumentListIcon className="h-24 w-24 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-600 mb-2">
              No boards yet
            </h2>
            <p className="text-gray-500 mb-6">
              Create your first board to start organizing your tasks
            </p>
            <Link href="/boards/new">
              <Button variant="primary">
                <PlusIcon className="h-5 w-5 mr-2" />
                Create Board
              </Button>
            </Link>
          </div>
        )}

        {/* Boards Grid */}
        {!loading && boards.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {boards.map((board) => (
              <Link key={board.id} href={`/boards/${board.id}`}>
                <Card className="hover:border-primary-300 transition-colors cursor-pointer h-full">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-primary-500 mb-2">
                        {board.name}
                      </h3>
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {board.description}
                      </p>
                    </div>
                    <div className="flex flex-col items-end space-y-1 ml-2">
                      {board.is_public && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                          Public
                        </span>
                      )}
                      {board.project && (
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                          {board.project.name}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 mr-1" />
                        <span>{board.task_count || 0} tasks</span>
                      </div>
                      <div className="flex items-center">
                        <UsersIcon className="h-4 w-4 mr-1" />
                        <span>{board.member_count || 0} members</span>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-200">
                    <p className="text-xs text-gray-400">
                      Updated {new Date(board.updated_at).toLocaleDateString()}
                    </p>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        )}
      </main>
    </div>
  );
};

export default function Boards() {
  return (
    <AuthProvider>
      <BoardsPage />
    </AuthProvider>
  );
}
