'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import AuthProvider from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import {
  PlusIcon,
  CheckCircleIcon,
  ClockIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';

interface Task {
  id: number;
  title: string;
  description: string;
  status: 'TODO' | 'IN_PROGRESS' | 'COMPLETED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  due_date: string | null;
  created_at: string;
  assignee?: {
    id: number;
    username: string;
  };
  board?: {
    id: number;
    name: string;
  };
}

const TasksPage: React.FC = () => {
  const { isAuthenticated, user: _user } = useAuth();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'assigned' | 'created'>('all');

  useEffect(() => {
    if (isAuthenticated) {
      fetchTasks();
    }
  }, [isAuthenticated, filter]);

  const fetchTasks = async () => {
    try {
      // TODO: Replace with actual API call
      // Mock data for now
      setTasks([
        {
          id: 1,
          title: "Design homepage mockup",
          description: "Create wireframes and mockups for the new homepage design",
          status: 'IN_PROGRESS',
          priority: 'HIGH',
          due_date: '2024-02-15T00:00:00Z',
          created_at: '2024-01-20T10:00:00Z',
          assignee: { id: 1, username: 'johndoe' },
          board: { id: 1, name: 'Website Redesign' }
        },
        {
          id: 2,
          title: "Set up development environment",
          description: "Configure local development environment with all necessary tools",
          status: 'COMPLETED',
          priority: 'MEDIUM',
          due_date: null,
          created_at: '2024-01-18T09:00:00Z',
          assignee: { id: 2, username: 'janedoe' },
          board: { id: 2, name: 'Mobile App' }
        },
        {
          id: 3,
          title: "Write API documentation",
          description: "Document all API endpoints with examples and response formats",
          status: 'TODO',
          priority: 'LOW',
          due_date: '2024-02-20T00:00:00Z',
          created_at: '2024-01-22T14:30:00Z',
          board: { id: 1, name: 'Website Redesign' }
        }
      ]);
    } catch (error) {
      console.error('Failed to fetch tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'IN_PROGRESS':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />;
    }
  };

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'HIGH':
        return 'bg-red-100 text-red-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'LOW':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800';
      case 'TODO':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-primary-500 mb-4">
            Please sign in to view your tasks
          </h1>
          <Link href="/">
            <Button variant="primary">Go to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-primary-500">Tasks</h1>
            <p className="text-gray-600 mt-2">
              Manage and track your tasks across all projects
            </p>
          </div>
          <Link href="/tasks/new">
            <Button variant="primary">
              <PlusIcon className="h-5 w-5 mr-2" />
              New Task
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-4 mb-6">
          <FunnelIcon className="h-5 w-5 text-gray-400" />
          <div className="flex space-x-2">
            <Button
              variant={filter === 'all' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setFilter('all')}
            >
              All Tasks
            </Button>
            <Button
              variant={filter === 'assigned' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setFilter('assigned')}
            >
              Assigned to Me
            </Button>
            <Button
              variant={filter === 'created' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setFilter('created')}
            >
              Created by Me
            </Button>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading tasks...</p>
          </div>
        )}

        {/* Tasks List */}
        {!loading && (
          <div className="space-y-4">
            {tasks.map((task) => (
              <Link key={task.id} href={`/tasks/${task.id}`}>
                <Card className="hover:border-primary-300 transition-colors cursor-pointer">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 mt-1">
                      {getStatusIcon(task.status)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-primary-500 mb-1">
                            {task.title}
                          </h3>
                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {task.description}
                          </p>
                        </div>
                        
                        <div className="flex items-center space-x-2 ml-4">
                          <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(task.priority)}`}>
                            {task.priority}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(task.status)}`}>
                            {task.status.replace('_', ' ')}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center space-x-4">
                          {task.board && (
                            <span>Board: {task.board.name}</span>
                          )}
                          {task.assignee && (
                            <span>Assigned to: {task.assignee.username}</span>
                          )}
                        </div>
                        
                        {task.due_date && (
                          <div className="flex items-center">
                            <ClockIcon className="h-4 w-4 mr-1" />
                            <span>Due {new Date(task.due_date).toLocaleDateString()}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && tasks.length === 0 && (
          <div className="text-center py-12">
            <CheckCircleIcon className="h-24 w-24 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-600 mb-2">
              No tasks found
            </h2>
            <p className="text-gray-500 mb-6">
              Create your first task to get started
            </p>
            <Link href="/tasks/new">
              <Button variant="primary">
                <PlusIcon className="h-5 w-5 mr-2" />
                Create Task
              </Button>
            </Link>
          </div>
        )}
      </main>
    </div>
  );
};

export default function Tasks() {
  return (
    <AuthProvider>
      <TasksPage />
    </AuthProvider>
  );
}
